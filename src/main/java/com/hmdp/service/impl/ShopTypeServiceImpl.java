package com.hmdp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.hmdp.dto.Result;
import com.hmdp.entity.ShopType;
import com.hmdp.mapper.ShopTypeMapper;
import com.hmdp.service.IShopTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.swing.plaf.ListUI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
public class ShopTypeServiceImpl extends ServiceImpl<ShopTypeMapper, ShopType> implements IShopTypeService {

    @Resource
    private RedisTemplate jsonRedisTemplate;


    @Override
    public Result queryTypeList() {
        //use redis hash to get list from redis
        List range = jsonRedisTemplate.opsForList().range("cache:shop:type", 0, -1);
        List<ShopType> shopTypeList = BeanUtil.copyToList(range, ShopType.class);

//        if(CollUtil.isNotEmpty(shopTypeList)){
//            return Result.ok(shopTypeList);
//        }

        //use mybatis plus to get list from db
        shopTypeList = query().orderByAsc("sort").list();
        if (shopTypeList == null) {
            return Result.fail("No shop type found");
        }
        jsonRedisTemplate.opsForList().leftPushAll("cache:shop:type", BeanUtil.copyToList(shopTypeList, ShopType.class));
        return Result.ok(shopTypeList);
    }


}
